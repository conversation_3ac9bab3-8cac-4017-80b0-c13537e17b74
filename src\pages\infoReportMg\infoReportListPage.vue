<route lang="json5">
{
  style: {
    navigationBarTitleText: '商品通报管理',
  },
}
</route>

<script lang="ts" setup>
import debounce from 'debounce'
import { DesignImage } from '@/components/image'
import { Color } from '@/enums/colorEnum'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const keyword = ref('')
const isShowFilter = ref(false)
const isFilter = ref(false)
const isShowInfoBar = ref(false)
const isShowDetail = ref(false)
const productList = ref([])

enum Status {
  fail = 0,
  done = 1,
  processing = 2,
}

productList.value = [
  {
    id: 1,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.done,
    statusStr: '已通报',
    brand: '鱼米之乡',
    reason: '',
  },
  {
    id: 2,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）食用木薯淀粉（吸嘴袋）食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋食用木薯淀粉食用木薯淀粉',
    specifications: '2.5kg×10/袋2.5kg×10/袋2.5kg×10/袋',
    status: Status.fail,
    statusStr: '通报失败',
    brand: '鱼米之乡鱼米之乡鱼米之乡鱼米之乡鱼米之乡鱼米之乡鱼米之乡鱼米之乡',
    reason: '失败原因：系统繁忙，请扫后尝试',
  },
  {
    id: 3,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.processing,
    statusStr: '通报中',
    brand: '鱼米之乡',
    reason: '',
  },
  {
    id: 4,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.fail,
    statusStr: '通报失败',
    brand: '鱼米之乡',
    reason: '失败原因：系统繁忙，请扫后尝试',
  },
  {
    id: 5,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.processing,
    statusStr: '通报中',
    brand: '鱼米之乡',
    reason: '',
  },
  {
    id: 6,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.processing,
    statusStr: '通报中',
    brand: '鱼米之乡',
    reason: '',
  },
  {
    id: 7,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.fail,
    statusStr: '通报失败',
    brand: '鱼米之乡',
    reason: '失败原因：系统繁忙，请扫后尝试',
  },
  {
    id: 8,
    code: '69773270406',
    name: '食用木薯淀粉（吸嘴袋）',
    netWeight: '10袋',
    specifications: '2.5kg×10/袋',
    status: Status.processing,
    statusStr: '通报中',
    brand: '鱼米之乡',
    reason: '',
  },
]

const handleInput = debounce(() => {
  handleSearch()
}, 800)

function handleSearch() {
  console.log(keyword.value)
}

function getStatusColor(status: number) {
  switch (status) {
    case Status.fail:
      return 'text-red-500'
    case Status.done:
      return 'text-emerald-500'
    case Status.processing:
      return 'text-primary'
    default:
      return 'text-gray-500'
  }
}

function showDetail(id: number) {
  uni.navigateTo({
    url: `/pages/infoReportMg/infoReportDetailPage?id=${id}`,
  })
}
</script>

<template>
  <view>
    <view class="f-search-bar flex items-center bg-white px-3 shadow-blue">
      <view class="o-bg-no flex flex-1 items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="keyword" :maxlength="14" border="none" class="o-bg-transparent grow" clearable
          placeholder="条码/商品名称" type="number" @change="handleInput"
        />
        <view
          :class="isFilter ? 'o-color-primary' : 'o-color-aid'" class="flex items-center gap-1 text-sm"
          @click="isShowFilter = !isShowFilter"
        >
          <view class="shrink-0">
            筛选/排序
          </view>
          <view style="min-width: 1.5rem">
            <up-icon v-if="isShowFilter" name="arrow-up" size="14" />
            <up-icon v-else name="arrow-down" size="14" />
          </view>
        </view>
      </view>
    </view>
    <scroll-view :scroll-y="true" class="o-bg-no f-scroll-view">
      <view class="p-3 space-y-2">
        <view v-for="item in productList" :key="item.id" class="rounded bg-white p-3" @click="showDetail(item.goodsId)">
          <view class="flex items-baseline justify-between text-sm leading-4">
            <view class="text-gray">
              {{ item.code }}
            </view>
            <view :class="getStatusColor(item.status)">
              {{ item.statusStr }}
            </view>
          </view>
          <view class="mt-1 flex items-baseline justify-between leading-4 space-x-4">
            <view class="line-clamp-2 flex-1 font-bold">
              {{ item.name }}
            </view>
            <view class="line-clamp-2 shrink-0 text-sm text-gray" style="max-width: 30vw">
              {{ item.brand }}
            </view>
          </view>
          <view class="mt-1 flex items-baseline justify-between text-sm text-gray leading-4">
            <view class="line-clamp-2" style="max-width: 40vw">
              净含量：{{ item.netWeight }}
            </view>
            <view class="line-clamp-2" style="max-width: 30vw">
              规格：{{ item.specifications }}
            </view>
          </view>
          <view v-if="item.status === Status.fail || item.status === Status.processing" class="o-line my-2" />
          <view v-if="item.status === Status.fail" class="text-sm text-red-500">
            {{ item.reason }}
          </view>
          <view class="mt-2 flex justify-end space-x-4">
            <view
              v-if="item.status === Status.fail || item.status === Status.processing"
              class="rounded bg-gray-100 px-4 py-2 text-gray-500"
            >
              取消通报
            </view>
            <view v-if="item.status === Status.fail" class="rounded bg-red-50 px-4 py-2 text-red-500">
              重试
            </view>
          </view>
        </view>
      </view>
      <view class="o-pb" />
    </scroll-view>
    <view
      class="pointer-events-none fixed right-0 box-border w-full p-3"
      style="bottom:calc(env(safe-area-inset-bottom) + 10rpx)"
    >
      <view
        v-if="!isShowInfoBar"
        class="o-tabbar-shadow pointer-events-auto float-right w-fit flex items-center rounded-full bg-white py-1 pl-1 pr-5 space-x-1"
        @click="isShowInfoBar = true"
      >
        <up-icon name="checkmark-circle-fill" size="50" :color="Color.emerald" />
        <view>
          <view class="text-sm">
            已完成所有通报
          </view>
          <view class="text-2xs text-gray">
            点击查看概况
          </view>
        </view>
      </view>
      <view
        v-if="isShowInfoBar"
        class="o-tabbar-shadow f-Info-box pointer-events-auto box-border w-full rounded-lg bg-white p-4"
      >
        <view class="mb-2 text-xl font-bold">
          产品通报概况
        </view>
        <view class="flex items-center justify-between text-gray">
          <view class="shrink-0">
            条码容量占用情况：
          </view>
          <view class="flex">
            <view>已使用：21</view>
            <view>
              <text class="px-1">
                /
              </text>总量：10000
            </view>
          </view>
        </view>
        <up-line-progress class="mt-2" :percentage="30" :active-color="Color.primary" />
        <view class="mt-3 flex items-center justify-between text-gray">
          <view class="shrink-0">
            快速通报情况：
          </view>
          <view class="text-emerald-500">
            已全部通报完成
          </view>
        </view>
        <view class="grid grid-cols-2 mt-3 gap-2">
          <view class="text-emerald-500">
            已通报：10000
          </view>
          <view class="text-gray-500">
            可通报数：10000
          </view>
          <view class="text-primary">
            通报中：10000
          </view>
          <view class="text-red-500">
            失败：10000
          </view>
        </view>
        <view class="flex items-center justify-between py-4 space-x-2">
          <view class="center flex-1 rounded bg-gray-100 py-2 text-amber-500">
            一键取消正在通报
          </view>
          <view class="center flex-1 rounded bg-red-50 py-2 text-red-500">
            一键重试失败通报
          </view>
        </view>
        <view class="flex items-end text-sm space-x-2">
          <view class="o-btn-light-bg-blue-border o-shadow-blue-light f-big-btn center flex-1 shrink-0 space-x-2">
            <image :src="DesignImage.btnIcon.cameras" mode="widthFix" class="f-btn-icon-md" />
            <text class="f-btn-text">
              拍照快速通报
            </text>
          </view>
          <view class="f-big-btn o-btn-light-bg-blue-border o-shadow-blue-light center shrink-0 flex-col px-4">
            <image :src="DesignImage.btnIcon.file" mode="widthFix" class="f-btn-icon-md" />
            <text class="f-btn-text">
              批量代办通报
            </text>
          </view>
          <view
            class="f-c-btn center shrink-0 rounded-full bg-primary text-xs text-white"
            @click="isShowInfoBar = false"
          >
            收起
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
$h: 96rpx;

.f-search-bar {
  height: $h;
}

.f-scroll-view {
  height: calc(100vh - $h);
}

.f-Info-box {
  border-radius: 6px 6px 36px 6px;
}

.f-big-btn {
  height: 146rpx;
}

.f-btn-icon-md {
  width: 106rpx;
}

.f-c-btn {
  $w: 74rpx;
  height: $w;
  width: $w;
}
</style>
