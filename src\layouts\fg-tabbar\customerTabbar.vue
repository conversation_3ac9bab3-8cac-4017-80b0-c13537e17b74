<script setup lang="ts">
import { tabbarStore } from './tabbar'

// 'i-carbon-code',
import { tabbarList as _tabBarList, cacheTabbarEnable, selectedTabbarStrategy, TABBAR_MAP } from './tabbarList'

const customTabbarEnable
  = selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE
    || selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITHOUT_CACHE

/** tabbarList 里面的 path 从 pages.config.ts 得到 */
const tabbarList = _tabBarList.map(item => ({ ...item, path: `/${item.pagePath}` }))

function selectTabBar(name: number) {
  const url = tabbarList[name].path
  tabbarStore.setCurIdx(name)
  if (cacheTabbarEnable) {
    uni.switchTab({ url })
  }
  else {
    uni.navigateTo({ url })
  }
}

// 根据当前页面路径设置正确的tabbarIndex
function updateTabbarIndex() {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentPath = `/${currentPage.route}`

    // 查找当前路径在tabbarList中的索引
    const index = tabbarList.findIndex(item => item.path === currentPath)
    if (index !== -1 && index !== tabbarStore.curIdx) {
      tabbarStore.setCurIdx(index)
    }
  }
}

onLoad(() => {
  // 解决原生 tabBar 未隐藏导致有2个 tabBar 的问题
  const hideRedundantTabbarEnable = selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE
  hideRedundantTabbarEnable
  && uni.hideTabBar({
    fail(err) {
      console.log('hideTabBar fail: ', err)
    },
    success(res) {
      console.log('hideTabBar success: ', res)
    },
  })

  // 初始化时更新tabbar索引
  updateTabbarIndex()
})

// 每次页面显示时更新tabbar索引
onShow(() => {
  updateTabbarIndex()
})

function handleTabbarClick(index: number) {
  selectTabBar(index)
}
</script>

<template>
  <view
    v-if="customTabbarEnable" class="fixed bottom-0 left-0 right-0 z-100 box-border w-full center px-3 pb-safe"
  >
    <view class="f-tabbar o-tabbar-shadow mb-2 flex flex-1 items-center justify-around overflow-hidden bg-white px-4">
      <view v-for="(item, index) in tabbarList" :key="index" class="flex-1" @click="handleTabbarClick(index)">
        <image class="f-image" :src="tabbarStore.curIdx === index ? item.selectedIconPath : item.iconPath" mode="aspectFit" />
        <!-- 预加载未选中和选中状态的图标 -->
        <image class="f-hidden" :src="item.iconPath" />
        <image class="f-hidden" :src="item.selectedIconPath" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-tabbar {
  height: var(--fv-tabbar-height);
  border-radius: var(--fv-tabbar-height);
}
.f-image {
  $w: 130rpx;
  width: $w;
  height: $w;
}

.f-hidden {
  width: 0;
  height: 0;
  opacity: 0;
  position: absolute;
  visibility: hidden;
}
</style>
