<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的信息通报',
  },
}
</route>

<script lang="ts" setup>
import type { AllReportOrderPageRes } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { PROJECT_ALL } from '@/components/descriptionStr'
import ReportSuccessStep from '@/components/infoReport/reportSuccessStep.vue'
import NeedPhoneForm from '@/components/needPhoneForm.vue'
import { OrderDirection } from '@/enums/httpEnum'
import { useToPath } from '@/hooks/useToPath'
import { allReportOrderPageApi, ReportStatus } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { reportAuth, userId } = storeToRefs(userStore)
const useOrderStore = orderStore()
const { orderCode } = storeToRefs(useOrderStore)

const { toPath } = useToPath()

const dataList = ref<AllReportOrderPageRes['data']>([])

const infoReportData = PROJECT_ALL.filter(item => item.descriptionServiceType === 'infoReport')[0]

onMounted(() => {
  if (reportAuth.value) {
    getDataList()
  }
})

function getDataList() {
  allReportOrderPageApi({
    orderBy: 'payDate',
    userId: userId.value,
    orderDirection: OrderDirection.def,
  }).then((res) => {
    dataList.value = res.data
    // 获取第一个未完成订单（如果未有满足条码，则获取第一个）的orderCode，用于传电话
    if (dataList.value.length > 0) {
      const firstOrder = dataList.value.find(item => item.reportStatus !== ReportStatus.success)
      if (firstOrder) {
        orderCode.value = firstOrder?.orderCode
      }
      else {
        orderCode.value = dataList.value[0].orderCode
      }
    }
  })
}

function getColor(reportStatus: ReportStatus) {
  switch (reportStatus) {
    case ReportStatus.dissatisfy:
      return 'color-red'
    case ReportStatus.processing:
      return 'color-yellow'
    case ReportStatus.success:
      return 'color-gray'
    default:
      return ''
  }
}
</script>

<template>
  <view v-if="reportAuth">
    <view class="bg-white px-6 pb-1 pt-6">
      <ReportSuccessStep />
    </view>
    <cs-long-button />
    <view class="bg-white px-2">
      <NeedPhoneForm />
    </view>
    <view class="px-4">
      <view class="mt-4 font-bold">
        通报服务情况：
      </view>
      <view v-for="item in dataList" :key="item.orderId" class="mt-2 flex rd-2 bg-white p-4">
        <view class="grow-1">
          <view class="text-xs color-gray">
            支付时间：{{ item.payDate }}
          </view>
          <view class="mb-2 text-xs color-gray">
            订单编号：{{ item.orderCode }}
          </view>
          <view>{{ item.orderContent }}</view>
        </view>
        <view class="shrink-0" :class="getColor(item.reportStatus)">
          {{ item.reportStatusStr }}
        </view>
      </view>
      <view class="py-10" />
    </view>
  </view>
  <view v-else class="bg-white p-6">
    <view class="mb-3 text-base font-bold">
      您未申请信息通报服务
    </view>
    <view class="o-p mb-3 text-sm">
      单纯条码并不能被识别，需要进行产品信息通报后才能投放市场。
    </view>
    <view
      class="o-bg-primary o-shadow-blue flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
      @click="toPath(infoReportData, 'navigateTo')"
    >
      前往办理通报业务
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
