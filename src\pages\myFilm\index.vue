<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '我的条码',
  },
}
</route>

<script lang="ts" setup>
import FreeDownPage from '@/components/myFilm/freeDownPage.vue'
import OrderDownPage from '@/components/myFilm/orderDownPage.vue'
import { Color } from '@/enums/colorEnum'

const tab = ref(0)
const list = [
  {
    name: '订单形式下载',
  },
  {
    name: '自由选择下载',
  },
]
</script>

<template>
  <up-tabs
    v-model:current="tab"
    class="bg-white"
    :line-color="Color.primary"
    :list="list"
    :line-width="90"
    :scrollable="false"
  />
  <order-down-page v-if="tab === 0" />
  <free-down-page v-if="tab === 1" />
</template>

<style lang="scss" scoped>
:deep(.u-tabs) {
  @apply bg-white;
}
</style>
