<route lang="json5">
{
  style: {
    navigationBarTitleText: '续展业务办理',
  },
}
</route>

<script setup lang="ts">
import type {
  OrderRenewalCreateParams,
  UploadImageRes,
} from '@/service/agencyServiceApi'
import { storeToRefs } from 'pinia'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { BASE64IMG_PREFIX, UPLOAD_IMG_MAXSIZE } from '@/enums'
import {
  orderRenewalCreateApi,
  uploadOrderOtherImageApiPath,
} from '@/service/agencyServiceApi'
import { submitServiceStore } from '@/store/submitServiceStore'
import { useUserStore } from '@/store/user'
import { validatePhoneNumber } from '@/utils/tool'

const userStore = useUserStore()
const useSubmitServiceStore = submitServiceStore()
const { orderInfoData } = storeToRefs(useSubmitServiceStore)
// 图片最大3M
const BASEURL = import.meta.env.VITE_SERVER_BASEURL
const isShowPassword = ref(true)
const isShowModal = ref(false)
const loading = ref(false)
const form = ref(null)
const companyLicenseFiles = ref([])
const companyLicenseChangeFiles = ref([])
const payCetificateFiles = ref([])

// 符合单向数据流
const model = reactive<OrderRenewalCreateParams>({
  barCodeCardNum: orderInfoData.value.barCodeCardNum ?? '',
  barCodeCardPassword: orderInfoData.value.barCodeCardPassword ?? '',
  companyLicenseChangeUrl: orderInfoData.value.companyLicenseChangeUrl ?? '',
  companyLicenseUrl: orderInfoData.value.companyLicenseUrl ?? '',
  contact: orderInfoData.value.contact ?? '',
  contactPhone: orderInfoData.value.contactPhone ?? '',
  email: orderInfoData.value.email ?? '',
  isHasChange: orderInfoData.value.isHasChange ?? false,
  orderId: orderInfoData.value.orderId ?? null,
  payCetificate: orderInfoData.value.payCetificate ?? '',
})

const rules = {
  contact: [{ required: true, message: '请输入联系人姓名', trigger: ['blur'] }],
  contactPhone: [
    {
      required: true,
      message: '请输入联系人手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  email: [
    {
      required: true,
      message: '请输入电子邮箱地址',
      trigger: ['blur'],
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.email(value)
      },
      message: '请输入正确的邮箱地址',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  barCodeCardNum: [{ required: true, message: '忘记？填注册所留联系手机', trigger: ['blur'] }],
}

// 删除图片
function deletePayCetificate(event) {
  payCetificateFiles.value.splice(event.index, 1)
  model.payCetificate = ''
}
function deleteCompanyLicense(event) {
  companyLicenseFiles.value.splice(event.index, 1)
  model.companyLicenseUrl = ''
}
function deleteCompanyLicenseChange(event) {
  companyLicenseChangeFiles.value.splice(event.index, 1)
  model.companyLicenseChangeUrl = ''
}

function payCetificateAfterRead(event) {
  handleFileUpload(event, payCetificateFiles, 'payCetificate')
}

function companyLicenseUrlAfterRead(event) {
  handleFileUpload(event, companyLicenseFiles, 'companyLicenseUrl')
}

function companyLicenseChangeAfterRead(event) {
  handleFileUpload(event, companyLicenseChangeFiles, 'companyLicenseChangeUrl')
}

async function handleFileUpload(event, filesRef, modelProperty: 'companyLicenseChangeUrl' | 'companyLicenseUrl' | 'payCetificate') {
  const lists = [].concat(event.file)
  let fileListLen = filesRef.value.length

  // 添加上传状态
  lists.forEach((item) => {
    filesRef.value.push({ ...item, status: 'uploading', message: '上传中' })
  })

  // 逐个上传并更新状态
  for (let i = 0; i < lists.length; i++) {
    const result = await uploadFilePromise(lists[i].url)
    const item = filesRef.value[fileListLen]
    // fileRef 的结构是up-upload的结构，不要变，只能加
    filesRef.value.splice(fileListLen, 1, {
      ...item,
      status: 'success',
      message: '',
      url: BASE64IMG_PREFIX + result.image,
      realUrl: result.url,
    })
    fileListLen++
  }

  // 更新模型属性
  model[modelProperty] = filesRef.value[0]?.realUrl || ''
}

function uploadFilePromise(url) {
  return new Promise<UploadImageRes['data']>((resolve, reject) => {
    const a = uni.uploadFile({
      url: BASEURL + uploadOrderOtherImageApiPath, // 仅为示例，非真实的接口地址
      filePath: url,
      name: 'file',
      header: {
        Authorization: userStore.authorization,
      },
      success: (res) => {
        console.log(res)
        const d = JSON.parse(res.data) as UploadImageRes
        if (d.success) {
          resolve(d.data)
        }
        else {
          reject(d.errMessage)
        }
      },
      fail: (err) => {
        console.log(err)
        reject(err)
      },
    })
  })
}

function handleSubmit() {
  if (loading.value)
    return
  form.value
    .validate()
    .then((valid) => {
      if (valid) {
        if (!model.payCetificate) {
          uni.showToast({
            title: '请上传付款凭证',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        if (!model.companyLicenseUrl) {
          uni.showToast({
            title: '请上传营业执执照复印件',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        if (model.isHasChange && !model.companyLicenseChangeUrl) {
          uni.showToast({
            title: '请上传变更后的营业执照复印件',
            icon: 'none',
            duration: 2000,
          })
          return
        }
        loading.value = true
        orderRenewalCreateApi({
          barCodeCardNum: model.barCodeCardNum,
          barCodeCardPassword: model.barCodeCardPassword,
          companyLicenseChangeUrl: model.companyLicenseChangeUrl,
          companyLicenseUrl: model.companyLicenseUrl,
          contact: model.contact,
          contactPhone: model.contactPhone,
          email: model.email,
          isHasChange: model.isHasChange,
          orderId: model.orderId,
          payCetificate: model.payCetificate,
        })
          .then(() => {
            isShowModal.value = true
          })
          .finally(() => {
            loading.value = false
          })
      }
    })
    .catch((err) => {
      console.log(err)
      uni.pageScrollTo({
        selector: '#fTop',
      })
      const title = err[0]?.message ?? '请输入必填项'
      uni.showToast({
        title,
        icon: 'none',
      })
    })
}

onMounted(() => {
  if (orderInfoData.value.companyLicenseUrl) {
    companyLicenseFiles.value = [
      {
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + orderInfoData.value.companyLicenseImage,
      },
    ]
  }
  if (orderInfoData.value.companyLicenseChangeUrl) {
    companyLicenseChangeFiles.value = [
      {
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + orderInfoData.value.companyLicenseChangeImage,
      },
    ]
  }
  if (orderInfoData.value.payCetificate) {
    payCetificateFiles.value = [
      {
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + orderInfoData.value.payCetificateImage,
      },
    ]
  }
})

function handleOk() {
  isShowModal.value = false
  uni.navigateBack()
}
</script>

<template>
  <view class="p-4">
    <up-form ref="form" label-width="80" :model="model" :rules="rules">
      <view id="fTop" class="rd-md bg-white py-4 pl-6 pr-4">
        <up-form-item label="联系人" prop="contact" required>
          <up-input v-model="model.contact" border="bottom" placeholder="联系人姓名" clearable />
        </up-form-item>
        <up-form-item label="联系手机" prop="contactPhone" required>
          <up-input
            v-model="model.contactPhone"
            border="bottom"
            type="number"
            :maxlength="11"
            clearable
            placeholder="联系手机"
          >
            <template #suffix>
              <text class="text-xs color-gray">
                {{ model.contactPhone.length }}/11
              </text>
            </template>
          </up-input>
        </up-form-item>
        <up-form-item label="电子邮箱" prop="email" required>
          <up-input
            v-model="model.email"
            clearable
            border="bottom"
            placeholder="用于接收办理结果通知"
          />
        </up-form-item>
      </view>
      <view class="mt-2 rd-md bg-white p-4">
        <up-form-item label="条码卡号" prop="barCodeCardNum">
          <up-input
            v-model="model.barCodeCardNum"
            border="bottom"
            clearable
            placeholder="忘记？填所注册留联系手机"
          />
        </up-form-item>
        <view class="flex items-center gap-1">
          <up-form-item label="条码卡密码" prop="barCodeCardPassword">
            <up-input
              v-model="model.barCodeCardPassword"
              border="bottom"
              :password="isShowPassword"
              clearable
              placeholder=""
            />
          </up-form-item>
          <view class="o-color-aid shrink-0 text-sm" @click="isShowPassword = !isShowPassword">
            {{ isShowPassword ? '隐藏' : '显示' }}
          </view>
        </view>
        <view class="o-color-aid text-right text-xs">
          <text class="pr-1 color-red">
            *
          </text>
          若忘记密码，可让客服通过手机验证码重置
        </view>
      </view>
    </up-form>
    <view class="o-color-aid o-p mt-3 text-sm">
      如未正确提供条码卡号及密码，请留意接听客服电话，或添加客服为好友，才可接收客服信息。
    </view>
    <cs-long-button content="添加您的专属客服" />
    <view class="mt-2 rd-md bg-white p-4">
      <view class="mb-2 mt-4">
        <text class="shrink-0 pr-1 color-red">
          *
        </text>
        <text>系统维护费汇款证明复印件或者扫描件：</text>
      </view>
      <up-upload
        class="pl-6"
        :file-list="payCetificateFiles"
        name="1"
        :multiple="false"
        :max-count="1"
        :max-size="UPLOAD_IMG_MAXSIZE"
        @after-read="payCetificateAfterRead"
        @delete="deletePayCetificate"
      />
      <view class="mb-2 mt-4 flex">
        <text class="shrink-0 pr-1 color-red">
          *
        </text>
        <text>营业执执照复印件加盖公章后的图片：</text>
      </view>
      <up-upload
        class="pl-6"
        :file-list="companyLicenseFiles"
        name="1"
        :multiple="false"
        :max-count="1"
        :max-size="UPLOAD_IMG_MAXSIZE"
        @after-read="companyLicenseUrlAfterRead"
        @delete="deleteCompanyLicense"
      />
      <template v-if="model.isHasChange">
        <view class="mb-2 mt-4 flex">
          <text class="shrink-0 pr-1 color-red">
            *
          </text>
          <text>营业执照变更证明（市场监管部门出具的）复印件加盖公章扫描件：</text>
        </view>
        <up-upload
          class="pl-6"
          :file-list="companyLicenseChangeFiles"
          name="1"
          :multiple="false"
          :max-count="1"
          :max-size="UPLOAD_IMG_MAXSIZE"
          @after-read="companyLicenseChangeAfterRead"
          @delete="deleteCompanyLicenseChange"
        />
      </template>
    </view>
    <view
      class="mt-4 flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
      :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
      @click="handleSubmit"
    >
      提交资料
    </view>
    <view class="p-8" />
    <up-modal
      :show="isShowModal"
      title="资料提交成功"
      content="如未正确提供条码卡号及密码，请留意接听客服电话，或添加客服为好友。"
      @confirm="handleOk"
    />
  </view>
</template>

<style scoped lang="scss"></style>
