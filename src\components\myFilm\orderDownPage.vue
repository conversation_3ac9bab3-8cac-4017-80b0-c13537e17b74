<script lang="ts" setup>
import type { OrderPageRes } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import { PROJECT_1 } from '@/components/descriptionStr'
import { BarType, PayState, ServerType } from '@/enums'
import { OrderDirection } from '@/enums/httpEnum'
import { useToPath } from '@/hooks/useToPath'
import BackTop from '@/pages/about/components/backTop/backTop.vue'
import { useScrollViewBackTop } from '@/pages/about/components/backTop/useScrollViewBackTop'
import { downloadByOrderApi } from '@/service/barcodePageApi'
import { orderPageApi } from '@/service/orderApi'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'
import { handleCopy } from '@/utils'
import { validateMail } from '@/utils/tool'

const useMsgModalStore = msgModalStore()
const { toPath } = useToPath()

const userStore = useUserStore()
const { userId, downloadEmail } = storeToRefs(userStore)

const showEmptyIcon = ref(false)
const page = ref(1)
const refreshTriggered = ref(false)
const isModalShow = ref(false)
const isLoading = ref(false)
const model = reactive({
  email: downloadEmail.value,
})
const data = ref<OrderPageRes['data']>([])
const allData = ref<OrderPageRes['data']>([])
let orderId: number

function getOrderData() {
  return new Promise((resolve, reject) => {
    orderPageApi({
      // serverType: ServerType.makeFilm, // 店内码也要囊括在内
      userId: userId.value,
      payState: PayState.paid,
      groupBy: '',
      needTotalCount: true,
      orderBy: 'payDate',
      orderDirection: OrderDirection.desc,
      pageIndex: page.value,
      pageSize: 10,
    })
      .then((res) => {
        const filteredData = res.data.filter(item =>
          [ServerType.makeFilm, ServerType.storeCode].includes(item.serverType),
        )

        if (page.value === 1) {
          // 首次加载或刷新
          allData.value = filteredData
        }
        else {
          // 加载更多
          allData.value = [...allData.value, ...filteredData]
        }

        data.value = allData.value

        // 判断是否已经到底了
        if (filteredData.length === 0 && page.value > 1) {
          showEmptyIcon.value = true
        }
        else if (filteredData.length < 50 && page.value > 1) {
          // 如果返回的数据少于页面大小，说明已经是最后一页了
          showEmptyIcon.value = true
        }

        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

onMounted(() => {
  getOrderData()
})

function onRefresh() {
  if (refreshTriggered.value)
    return
  refreshTriggered.value = true
  showEmptyIcon.value = false
  page.value = 1
  getOrderData().finally(() => {
    refreshTriggered.value = false
  })
}

// 滚动到底部加载更多
function onScrollToLower() {
  if (!showEmptyIcon.value && !refreshTriggered.value) {
    page.value++
    getOrderData()
  }
}

function handleDown(id: number) {
  isModalShow.value = true
  orderId = id
}

function getQuantity(startBarCode: string, endBarCode?: string) {
  // 去掉startBarCode最后一位
  const startNum = Number(startBarCode.slice(0, -1))
  if (endBarCode) {
    const endNum = Number(endBarCode.slice(0, -1))
    return endNum - startNum + 1
  }
  else {
    return 1
  }
}

const { scrollTop, flag, getScroll, getToTop } = useScrollViewBackTop()

function handleModalOk() {
  if (isLoading.value)
    return
  if (model.email === '') {
    uni.showToast({
      icon: 'none',
      title: '请输入邮箱地址',
    })
  }
  else if (!validateMail(model.email)) {
    uni.showToast({
      icon: 'none',
      title: '请输入正确的邮箱地址',
    })
  }
  else {
    isLoading.value = true
    uni.showLoading({
      title: '提交申请中',
    })
    downloadByOrderApi({
      downloadType: 2,
      email: model.email,
      orderId,
      userId: userId.value,
    })
      .then(() => {
        downloadEmail.value = model.email
        // showSuccess('已安排发送到邮箱')
        uni.hideLoading()
        isModalShow.value = false
        useMsgModalStore.alert({
          title: '已安排发送到邮箱',
          content:
            '鉴于条码制作需要时间，且邮件收发速度受网络影响，如果长时间没有收到邮件，请再提交申请，或咨询客服。谢谢！',
        })
      })
      .catch(() => {
        uni.hideLoading()
      })
      .finally(() => {
        isLoading.value = false
      })
  }
}

function handleToMakeFilm() {
  const p = PROJECT_1.find(item => item.descriptionServiceType === BarType.EAN13)
  toPath(p, 'navigateTo')
}
</script>

<template>
  <up-modal
    :show="isModalShow" title="选择下载方式" confirm-text="提交邮箱" show-cancel-button close-on-click-overlay
    @confirm="handleModalOk" @cancel="isModalShow = false" @close="isModalShow = false"
  >
    <view>
      <view class="text-left">
        <view class="font-bold">
          方式一：通过邮件下载
        </view>
        <up-input v-model="model.email" border="bottom" placeholder="请输入接收邮箱" />
        <view class="mt-6 font-bold">
          方式二：通过电脑下载
        </view>
        <view class="flex text-xs">
          用电脑打开下面网站，前往【条码制作】-【条码下载】，直接下载。
        </view>
        <view class="flex items-center justify-center gap-1 py-2" @click="handleCopy('www.gs1helper.com')">
          <view class="o-barcode-gray-card rd-1 px-6 py-1">
            www.gs1helper.com
          </view>
          <up-tag size="mini" plain type="warning" text="点击复制" />
        </view>
      </view>
    </view>
  </up-modal>
  <scroll-view
    :scroll-y="true" class="f-scroll-box o-bg-no relative" :scroll-top="scrollTop" :refresher-enabled="true"
    :refresher-triggered="refreshTriggered" :scroll-with-animation="true" @scroll="getScroll"
    @refresherrefresh="onRefresh" @scrolltolower="onScrollToLower"
  >
    <view v-for="item in data" :key="item.orderCode" class="px-3 pt-2">
      <view class="rd-2 bg-white p-4">
        <view class="flex items-center">
          <view class="flex-grow-1">
            <view class="o-color-aid text-xs">
              成交时间：{{ item.payDate }}
            </view>
            <view class="o-color-aid text-xs">
              订单编号：{{ item.orderCode }}
            </view>
          </view>
          <view
            class="o-bg-primary o-shadow-blue flex items-center justify-center rd-2 px-6 py-2 text-sm color-white"
            @click="handleDown(item.orderId)"
          >
            下载
          </view>
        </view>
        <view class="o-line mb-2 mt-2" />
        <view class="o-row-scroll mb-2 flex items-center">
          <view class="o-barcode-gray-card rd-1">
            {{ item.startBarCode }}
          </view>
          <template v-if="item.endBarCode !== item.startBarCode">
            <view>~</view>
            <view class="o-barcode-gray-card rd-1">
              {{ item.endBarCode }}
            </view>
          </template>
        </view>
        <view class="flex items-end justify-between">
          <view class="text-xs">
            放大系数为：{{ item.size }}
          </view>

          <view class="flex justify-end">
            <view class="flex items-baseline">
              <view class="text-sm">
                合计：
              </view>
              <view class="font-bold">
                {{ getQuantity(item.startBarCode, item.endBarCode) }}
              </view>
              <view class="ml-1 text-sm">
                张
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="data?.length === 0" class="p-3">
      <template v-if="isLoading = false">
        <up-empty icon="https://wx.gs1helper.com/images/common/content.png" text="暂无订单" />
        <view
          class="o-bg-primary o-shadow-blue mt-4 flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
          @click="handleToMakeFilm"
        >
          前往条码制作
        </view>
      </template>
      <up-loading-icon v-else class="py-10" mode="circle" />
    </view>
    <view v-if="showEmptyIcon" class="o-color-aid w-full py-6 text-center text-xs">
      - 已经到底了 -
    </view>
    <view class="o-pb" />
  </scroll-view>
  <back-top v-if="flag" @tap="getToTop" />
</template>

<style lang="scss" scoped>
.f-scroll-box {
  height: calc(100vh - 2.9rem);
}
</style>
