// pealpool: 泛型推导已改造
import type { DefaultDataRes } from '@/interceptors/myInterceptors'

interface IUseRequestOptions<T> {
  /** 是否立即执行 */
  immediate?: boolean
  /** 初始化数据 */
  initialData?: T
  /** 数据改造 */
  transform?: (data: T) => any
}

/**
 * useRequest是一个定制化的请求钩子，用于处理异步请求和响应。
 * @param func 一个执行异步请求的函数，返回一个包含响应数据的Promise。
 * @param options 包含请求选项的对象 {immediate, initialData}。
 * @param options.immediate 是否立即执行请求，默认为false。
 * @param options.initialData 初始化数据，默认为undefined。
 * @param options.transform 改造响应数据的函数，默认为undefined。
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export default function useRequest<T extends DefaultDataRes = DefaultDataRes>(
  func: () => Promise<T>,
  options: IUseRequestOptions<T['data']> = { immediate: false },
) {
  const loading = ref(false)
  const error = ref(false)
  const data: Ref<T['data'] | undefined> = ref(options.initialData)
  const pageIndex = ref(1)
  const totalCount = ref(0)
  const totalPages = ref(0)

  const run = async () => {
    loading.value = true
    return func()
      .then((res) => {
        if (options.transform) {
          data.value = options.transform(res.data)
        }
        else {
          data.value = res.data
        }
        error.value = false
        pageIndex.value = res?.pageIndex ?? 1
        totalCount.value = res?.totalCount ?? 0
        totalPages.value = res?.totalPages ?? 0
        loading.value = false
        return data.value
      })
      .catch((err) => {
        loading.value = false
        error.value = err
        throw err
      })
  }

  options.immediate && run()
  return { loading, error, data, pageIndex, totalCount, totalPages, run }
}
