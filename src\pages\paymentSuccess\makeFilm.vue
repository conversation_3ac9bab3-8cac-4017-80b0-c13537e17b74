<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '支付成功',
  },
}
</route>

<script lang="ts" setup>
import type { ServerType } from '@/enums'
import { storeToRefs } from 'pinia'
import { makeFilmAndReportServiceDescription, PROJECT_ALL } from '@/components/descriptionStr'
import CouponCard from '@/components/Price/CouponCard.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { useToPath } from '@/hooks/useToPath'
import { getCouponApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'

const useOrderStore = orderStore()
const { orderCode, startBarCode, endBarCode, size, price, useCount, actuallyPrice }
  = storeToRefs(useOrderStore)
const serviceStore = useServiceStore()
const { isHasOtherServer } = storeToRefs(serviceStore)
const { toPath } = useToPath()

const couponName = ref('')
const couponPrice = ref(0)
const couponType = ref(2)
const description = ref('')
const discount = ref(0)
const serverType = ref<ServerType>()
const expirySeconds = ref(0)
const vendorCode = ref(0)
const showCoupon = ref(false)
const labelPrintData = PROJECT_ALL.filter(item => item.descriptionServiceType === 'labelPrint')[0]
const infoReportData = PROJECT_ALL.filter(item => item.descriptionServiceType === 'infoReport')[0]

getCouponApi({ orderCode: orderCode.value }).then((res: any) => {
  console.log(res)
  const d = res.data
  if (d.couponId) {
    couponName.value = d.couponName
    couponPrice.value = d.couponPrice
    couponType.value = d.couponType
    description.value = d.description
    discount.value = d.discount
    serverType.value = d.serverType
    expirySeconds.value = d.expirySeconds
    vendorCode.value = d.vendorCode
    showCoupon.value = true
  }
})

function handleToMyOrder() {
  uni.switchTab({
    url: '/pages/myFilm/index',
  })
}
</script>

<template>
  <view class="px-4 pb-10 pt-4">
    <view class="o-color-primary pl-3 text-xl font-bold">
      订单支付成功！
    </view>
    <view v-if="showCoupon" class="o-color-primary pl-3 text-sm">
      恭喜获得一张折扣券
    </view>
    <view class="mt-2 rd-2 bg-white p-4">
      <view class="o-color-aid text-xs">
        条码制作
      </view>
      <view class="o-color-aid mb-2 text-xs">
        订单编号：{{ orderCode }}
      </view>
      <view class="mb-2 flex items-center">
        <view class="o-barcode-gray-card rd-1">
          {{ startBarCode }}
        </view>
        <template v-if="endBarCode !== startBarCode">
          <view>~</view>
          <view class="o-barcode-gray-card rd-1">
            {{ endBarCode }}
          </view>
        </template>
      </view>
      <view class="flex items-end justify-between gap-3">
        <view class="text-xs">
          <view>放大系数为：{{ size }}</view>
          <view>
            <text class="font-bold">
              {{ useCount }}
            </text>
            张
            <text>×</text>
            <text class="pl-1 font-bold">
              {{ price }}
            </text>
            元/张
          </view>
          <view v-if="isHasOtherServer">
            含{{ makeFilmAndReportServiceDescription }}
          </view>
        </view>
        <view class="flex shrink-0 justify-end">
          <view class="flex items-baseline">
            <view class="text-sm">
              实付：
            </view>
            <price-box :price="actuallyPrice" :size="48" />
          </view>
        </view>
      </view>
      <view class="o-line mb-4 mt-4" />
      <view class="mb-2 text-sm font-bold">
        如何下载
      </view>
      <view class="mb-3 text-xs">
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>等待系统制作订单（通常1秒内完成，量大时可关闭小程序，等待10分钟）。</view>
        </view>
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>
            前往
            <text class="o-color-primary font-bold">
              我的条码
            </text>
            按需下载。
          </view>
        </view>
      </view>
      <view
        class="f-light-btn flex flex-grow-1 items-center justify-center rd-2 p-3 text-primary"
        @click="handleToMyOrder"
      >
        前往下载
      </view>
    </view>
    <!--    <NeedPhoneForm v-if="isHasOtherServer" /> -->
    <view class="mt-3 rd-2 bg-white">
      <view class="p-4">
        <view class="mb-2 text-base color-red font-bold">
          温馨提示
        </view>
        <view class="o-p mb-3 text-sm">
          单纯条码并不能被识别，需要进行产品信息通报后才能投放市场。
        </view>
        <view
          class="o-bg-primary o-shadow-blue flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
          @click="toPath(infoReportData, 'switchTo')"
        >
          前往办理通报业务
        </view>
      </view>
      <template v-if="showCoupon">
        <view class="f-coupon-dot mb-4 w-full" />
        <view class="p-4">
          <coupon-card
            :data="{
              couponName,
              couponPrice,
              couponType,
              description,
              serverType,
              discount,
              expirySeconds,
              vendorCode,
            }"
          />
        </view>
      </template>
    </view>
    <view class="mt-1 p-4 text-lg font-bold">
      条码如何制作成标签？
    </view>
    <view
      class="mb-2 flex items-center gap-3 rd-2 bg-white p-2"
      @click="toPath(labelPrintData, 'switchTo')"
    >
      <up-image :height="76.41" :src="labelPrintData.image" :width="110" />
      <view class="flex grow flex-col gap-1">
        <view class="text-base font-bold">
          {{ labelPrintData.title }}
        </view>
        <view>
          <view
            class="o-tag o-color-primary float-left rd-1"
            :style="{ color: labelPrintData.tagStrColor, background: labelPrintData.tagBgColor }"
          >
            {{ labelPrintData.typeStr }}
          </view>
        </view>
        <view class="o-color-aid text-xs">
          {{ labelPrintData.description }}
        </view>
      </view>
      <view class="o-color-aid pr-1">
        <up-icon name="arrow-right" size="14" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-coupon-dot {
  height: 50rpx;
  background-image: url('https://wx.gs1helper.com/images/p_coupon_o_o_o.png');
  background-repeat: no-repeat;
  background-position-y: 50%;
  background-size: contain;
}

.f-light-btn {
  border: 1px solid var(--wot-color-theme);
}
</style>
