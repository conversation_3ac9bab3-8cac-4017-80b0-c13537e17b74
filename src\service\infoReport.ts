import type { GoodsType, InfoReportBarType, SyncAddState, SyncState } from '@/enums'
import { http } from '@/utils/http'

export interface ReportGoodsLoadV2Res {
  data: {
    attribueList: {
      attributeId: string
      attributeName: string
      attributeValue: string
    }[]
    barCode: string
    barType: InfoReportBarType
    brandName: string
    commonName: string
    companyPrice: number
    currency: string
    goodsDescription: string
    goodsId: number
    goodsName: string
    goodsType: GoodsType
    gpcType: string
    gpcTypeName: string
    imageList: {
      imageDecs: string
      imageUrl: string
      isMain: boolean
    }[]
    isPrivary: boolean
    marketDate: string
    netContent: string
    netContentUnit: string
    packageCode: number
    productFeatures: string
    spec: string
    standardList: {
      executeStandard: string
      executeYear: string
      standardNumber: string
    }[]
    syncAddDate: string
    syncAddMsg: string
    syncAddState: SyncAddState
    syncAddStateName: string
    syncDate: string
    syncMsg: string
    syncState: SyncState
    syncStateName: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 加载通报商品V2
 * @param {string} goodsId 商品id
 * @returns
 */
export function reportGoodsLoadV2Api(params: { goodsId: number }) {
  return http.post<ReportGoodsLoadV2Res>('/api/reportGoodsLoadV2', params, undefined, false, {
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 参数接口
export interface ReportGoodsPageParams {
  barCode?: string
  brandName?: string
  certificationId?: number
  endDate?: string
  goodsName?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: string
  pageIndex?: number
  pageSize?: number
  startDate?: string
  userId: number
}

// 响应接口
export interface ReportGoodsPageRes {
  data: {
    barCode: string
    barType: InfoReportBarType
    brandName: string
    certificationId: number
    commonName: string
    companyPrice: number
    currency: string
    goodsDescription: string
    goodsId: number
    goodsName: string
    goodsType: GoodsType
    gpcType: string
    gpcTypeName: string
    isPrivary: boolean
    lastUpdatedDate: string
    marketDate: string
    netContent: string
    productFeatures: string
    spec: string
    syncAddDate: string
    syncAddMsg: string
    syncAddState: SyncAddState
    syncAddStateName: string
    syncDate: string
    syncMsg: string
    syncState: SyncState
    syncStateName: string
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 通报商品分页
 * @param {object} params qry
 * @param {string} params.barCode 条码信息
 * @param {string} params.brandName 品牌名称
 * @param {number} params.certificationId 认证id/企业id，因为前期不需要认证，所以不需要填写
 * @param {string} params.endDate 修改结束时间
 * @param {string} params.goodsName 产品名称
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {string} params.startDate 修改开始时间
 * @param {number} params.userId 用户id
 * @returns
 */
export function reportGoodsPageApi(params: ReportGoodsPageParams) {
  return http.post<ReportGoodsPageRes>('/api/reportGoodsPage', params)
}

// 参数接口
export interface ReportGoodsAddParams {
  attribueList?: {
    attributeId: string
    attributeName?: string
    attributeValue?: string
  }[]
  barCode: string
  barType: InfoReportBarType
  brandName: string
  commonName: string
  companyPrice?: number
  currency?: string
  goodsDescription: string
  goodsName: string
  goodsType: GoodsType
  gpcType: string
  imageList: {
    imageDecs?: string
    imageUrl?: string
    isMain?: boolean
  }[]
  isPrivary: boolean
  marketDate?: string
  netContent: string
  netContentUnit: string
  productFeatures?: string
  spec: string
  standardList?: {
    executeStandard?: string
    executeYear?: string
    standardNumber?: string
  }[]
  userId: number
}

// 响应接口
export interface ReportGoodsAddRes {
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 新增通报商品
 * @param {object} params cmd
 * @param {Array} params.attribueList 产品属性列表,根据分类不同，属性不同
 * @param {string} params.barCode 条码
 * @param {string} params.barType 条码类型:UPC-A/EAN/ITF
 * @param {string} params.brandName 品牌名称
 * @param {string} params.commonName 产品通用名
 * @param {number} params.companyPrice 企业定价,如果填写，要填写大于0的数字，如果币种选了，价格一定要填写
 * @param {string} params.currency 币种,如：人民币
 * @param {string} params.goodsDescription 产品描述
 * @param {string} params.goodsName 产品名称
 * @param {string} params.goodsType 产品状态填写“在产或不在产”
 * @param {string} params.gpcType 产品（GPC）分类
 * @param {Array} params.imageList 产品图片列表
 * @param {boolean} params.isPrivary 是否保密
 * @param {string} params.marketDate （预计）上市时间
 * @param {string} params.netContent 净含量
 * @param {string} params.netContentUnit 净含量单位
 * @param {string} params.productFeatures 产品特征
 * @param {string} params.spec 规格
 * @param {Array} params.standardList 产品标准列表
 * @param {number} params.userId 用户id
 * @returns
 */
export function reportGoodsAddApi(params: ReportGoodsAddParams) {
  return http.post<ReportGoodsAddRes>('/api/reportGoodsAdd', params)
}
