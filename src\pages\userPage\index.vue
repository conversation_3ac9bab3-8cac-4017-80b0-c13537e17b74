<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '用户信息',
    navigationStyle: 'custom',
  },
}
</route>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { ServerType } from '@/enums'
import { useUserStore } from '@/store/user'

const { safeAreaInsets } = uni.getSystemInfoSync()
const userStore = useUserStore()
const {
  realName,
  userCode,
  inBlacklist,
  registerServiceAuth,
  modifyServiceAuth,
  existUncompletedRegister,
  existUncompletedRenewal,
  existUncompletedChange,
} = storeToRefs(userStore)
const miniShopStatusStr = ref('')
const tempName = ref()

onShow(() => {
  userStore.login().then(() => {
    userStore.getUserServerInfo().then((res) => {
      if (res.data.qrAuth && res.data.qrOrderInfo?.expiryDate) {
        miniShopStatusStr.value
          = `到期时间：${dayjs(res.data.qrOrderInfo.expiryDate).format('YYYY-MM-DD')}`
        tempName.value = res.data.qrOrderInfo.tempName
      }
      else {
        miniShopStatusStr.value = '未开通'
      }
    })
  })
})

function toMyInformation() {
  uni.navigateTo({
    url: '/pages/userPage/myInformation',
  })
}
</script>

<template>
  <view class="f-bg overflow-hidden p-4">
    <view :style="{ marginTop: `${safeAreaInsets?.top}px` }">
      <view class="flex justify-between px-4 pb-4 pt-8">
        <view>
          <view class="o-color-aid text-xs">
            用户编号
          </view>
          <view class="text-lg">
            {{ realName }}
          </view>
        </view>
        <view class="center flex-self-end rd-1 bg-white p-2" @click="toMyInformation">
          <up-icon class="color-gray" name="setting" size="16" />
        </view>
      </view>
      <template v-if="!inBlacklist">
        <up-cell-group custom-class="bg-white py-2 rd-2" :border="false">
          <!--  #ifndef MP-TOUTIAO -->
          <!-- <up-cell title="物流订单" is-link url="/pages/myOrder/logisticsOrder" /> -->
          <!--  #endif -->
          <up-cell title="订单开票" is-link url="/pages/myOrder/index" />
          <up-cell title="红包卡券" is-link url="/pages/discountCoupon/index" :border="false" />
        </up-cell-group>
        <up-cell-group custom-class="bg-white py-2 rd-2 mt-3" :border="false">
          <up-cell title="我的信息通报" is-link url="/pages/infoReport/myInfoReport" />
          <up-cell
            v-if="registerServiceAuth" title="我的注册" is-link
            :url="`/pages/myAgency/index?serverType=${ServerType.registerService}`"
          >
            <template v-if="existUncompletedRegister" #value>
              <up-badge :is-dot="true" type="error" />
            </template>
          </up-cell>
          <up-cell
            v-if="modifyServiceAuth" title="我的变更" is-link
            :url="`/pages/myAgency/index?serverType=${ServerType.modifyService}`"
          >
            <template v-if="existUncompletedChange" #value>
              <up-badge :is-dot="true" type="error" />
            </template>
          </up-cell>
          <up-cell
            title="我的续展" is-link :border="false"
            :url="`/pages/myAgency/index?serverType=${ServerType.renewalService}`"
          >
            <template v-if="existUncompletedRenewal" #value>
              <up-badge :is-dot="true" type="error" />
            </template>
          </up-cell>
          <!-- <up-cell
            title="二维码微站"
            :label="tempName"
            :value="miniShopStatusStr"
            :border="false"
          /> -->
        </up-cell-group>
        <up-cell-group custom-class="bg-white py-2 rd-2 mt-3" :border="false">
          <up-cell title="胶片使用说明" is-link url="/pages/about/filmSpecification" />
          <up-cell title="关于" is-link url="/pages/about/index" :border="false" />
        </up-cell-group>
        <cs-long-button />
        <view class="o-color-aid mt-3 text-center text-xs">
          <text class="o-color-danger">
            *
          </text>
          采购合同等可咨询客服
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-bg {
  background-image: url('https://wx.gs1helper.com/images/p_user_bg.jpg');
  background-repeat: no-repeat;
  background-size: 100% auto;
}
</style>
