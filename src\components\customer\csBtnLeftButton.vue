<script setup lang="ts">
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'
</script>

<template>
  <!--  #ifndef MP-TOUTIAO -->
  <button class="f-cus-btn" @click="jumpToWeChatCustomerService">
    <image
      class="o-cs-img-small mt--5"
      src="https://wx.gs1helper.com/images/p_index_customer_service.png"
    />
    <view class="mt--4.5 text-xs color-gray">
      客服
    </view>
  </button>
  <!--  #endif -->
  <!--  #ifdef MP-TOUTIAO -->

  <button class="f-cus-btn" open-type="im" data-im-id="77222292537">
    <image
      class="o-cs-img-small mt--5"
      src="https://wx.gs1helper.com/images/p_index_customer_service.png"
    />
    <view class="mt--4.5 text-xs color-gray">
      客服
    </view>
  </button>
  <!--  #endif -->
</template>

<style scoped lang="scss">
.f-cus-btn {
  background: none;

  &::after {
    border: none;
  }
}
</style>
