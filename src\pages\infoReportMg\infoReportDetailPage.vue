<route lang="json5">
{
  style: {
    navigationBarTitleText: '产品详情',
    enablePullDownRefresh: true,
  },
}
</route>

<script lang="ts" setup>
import type { ReportGoodsLoadV2Res } from '@/service/infoReport'
import { reportGoodsLoadV2Api } from '@/service/infoReport'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const data = ref<ReportGoodsLoadV2Res['data']>()
const goodsId = ref<number>()

function getDetail() {
  reportGoodsLoadV2Api({
    // goodsId: goodsId.value
    goodsId: 936,
  }).then((res) => {
    data.value = res.data
    uni.stopPullDownRefresh()
  })
}

onLoad((options) => {
  goodsId.value = Number(options.id)
  getDetail()
})

onPullDownRefresh(() => {
  getDetail()
})
// TODO attribueList 缺值
// TODO packageCode 包装指示符有没有包含到barCode中？
// TODO standardList未写
</script>

<template>
  <view class="bg-white p-6">
    <scroll-view
      v-if="data?.imageList.length > 0" :scroll-x="true" :show-scrollbar="false" class="w-full"
      @touchmove.stop @scroll.stop
    >
      <view class="w-max flex space-x-2">
        <image
          v-for="(item, index) in data.imageList" :key="index" :src="item.imageUrl" mode="aspectFit"
          class="f-img-item shrink-0 rounded"
        />
      </view>
    </scroll-view>
    <view class="mt-3 flex items-center space-x-4">
      <view class="o-tag border border-primary rounded border-solid bg-primary/10 text-primary">
        {{ data?.barType }}
      </view>
      <view class="text-sm text-gray-500">
        {{ data?.barCode }}
      </view>
    </view>
    <view class="mb-4 mt-1 font-bold leading-4">
      {{ data?.goodsName }}
    </view>
    <view v-if="data?.commonName != null && data?.commonName !== undefined" class="f-d-box">
      <view class="f-d-title">
        通用名：
      </view>
      <view class="f-d-content">
        {{ data?.commonName }}
      </view>
    </view>
    <view v-if="data?.isPrivary != null && data?.isPrivary !== undefined" class="f-d-box">
      <view class="f-d-title">
        是否保密：
      </view>
      <view class="f-d-content">
        {{ data?.isPrivary ? '是' : '否' }}
      </view>
    </view>
    <view v-if="data?.brandName != null && data?.brandName !== undefined" class="f-d-box">
      <view class="f-d-title">
        品牌：
      </view>
      <view class="f-d-content">
        {{ data?.brandName }}
      </view>
    </view>
    <view v-if="data?.productFeatures != null && data?.productFeatures !== undefined" class="f-d-box">
      <view class="f-d-title">
        产品特征：
      </view>
      <view class="f-d-content">
        {{ data?.productFeatures }}
      </view>
    </view>
    <view v-if="(data?.netContent != null && data?.netContent !== undefined) || (data?.netContentUnit != null && data?.netContentUnit !== undefined)" class="f-d-box">
      <view class="f-d-title">
        净含量：
      </view>
      <view class="f-d-content">
        {{ data?.netContent }}{{ data?.netContentUnit }}
      </view>
    </view>
    <view v-if="data?.spec != null && data?.spec !== undefined" class="f-d-box">
      <view class="f-d-title">
        规格：
      </view>
      <view class="f-d-content">
        {{ data?.spec }}
      </view>
    </view>
    <view v-if="data?.gpcTypeName != null && data?.gpcTypeName !== undefined" class="f-d-box">
      <view class="f-d-title">
        GCP分类：
      </view>
      <view class="f-d-content">
        {{ data?.gpcTypeName }}
      </view>
    </view>
    <view v-if="data?.goodsDescription != null && data?.goodsDescription !== undefined" class="f-d-box">
      <view class="f-d-title">
        产品描述：
      </view>
      <view class="f-d-content">
        {{ data?.goodsDescription }}
      </view>
    </view>
    <view v-if="data?.marketDate != null && data?.marketDate !== undefined" class="f-d-box">
      <view class="f-d-title">
        上市(预计)时间：
      </view>
      <view class="f-d-content">
        {{ data?.marketDate }}
      </view>
    </view>
    <view v-if="data?.companyPrice != null && data?.companyPrice !== undefined" class="f-d-box">
      <view class="f-d-title">
        企业定价：
      </view>
      <view class="f-d-content">
        {{ data?.companyPrice }}
      </view>
    </view>
    <view v-if="data?.currency != null && data?.currency !== undefined" class="f-d-box">
      <view class="f-d-title">
        币种：
      </view>
      <view class="f-d-content">
        {{ data?.currency }}
      </view>
    </view>
    <view v-if="data?.goodsType != null && data?.goodsType !== undefined" class="f-d-box">
      <view class="f-d-title">
        产品状态：
      </view>
      <view class="f-d-content">
        {{ data?.goodsType }}
      </view>
    </view>
    <view class="py-12" />
  </view>
  <div class="fixed bottom-0 left-0 w-full bg-white p-6">
    <view class="f-d-box">
      <view class="f-d-title">
        通报状态：
      </view>
      <view class="f-d-content" :class="data?.syncAddState ? 'text-emerald-500' : 'text-red-500'">
        {{ data?.syncAddStateName }}
      </view>
    </view>
    <view class="f-d-box">
      <view class="f-d-title">
        微信共享：
      </view>
      <view class="f-d-content" :class="data?.syncState ? 'text-emerald-500' : 'text-red-500'">
        {{ data?.syncStateName }}
      </view>
    </view>
    <view class="f-d-box">
      <view class="f-d-title">
        通报失败：
      </view>
      <view class="f-d-content text-red-500">
        <view> {{ data?.syncAddMsg }} </view>
        <view>{{ data?.syncMsg }}</view>
      </view>
    </view>
  </div>
</template>

<style lang="scss" scoped>
.f-img-list {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  /* 增加iOS滚动流畅度 */
  scrollbar-width: none;
  /* Firefox */
}

.f-img-list::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}

.f-img-item {
  $w: 244rpx;
  width: $w;
  height: $w;
  border: 1px solid #ccc;
}

.f-d-box {
  @apply flex text-sm leading-4 mt-2;
}

.f-d-title {
  @apply text-gray shrink-0;
  width: 150rpx;
}
</style>
