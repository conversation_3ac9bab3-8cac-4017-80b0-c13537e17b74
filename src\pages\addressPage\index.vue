<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '收货地址',
    enablePullDownRefresh: true,
    backgroundColor: '#f0f3f8',
  },
}
</route>

<script lang="ts" setup>
import type { AddressPageRes } from '@/service/addressPageApi'
import { storeToRefs } from 'pinia'
import { OrderDirection } from '@/enums/httpEnum'
import {
  addressDelApi,
  addressPageApi,
  addressUpdateApi,
} from '@/service/addressPageApi'
import { addressStore } from '@/store/addressStore'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'

const useMsgModalStore = msgModalStore()
const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useAddressStore = addressStore()
const { addressData, addressId, selectAddressId } = storeToRefs(useAddressStore)

const toSelect = ref(false)

const { loading, error, data, run } = useRequest<AddressPageRes>(() =>
  addressPageApi({
    // isDefault: false,
    // groupBy: "",
    // needTotalCount: true,
    orderBy: 'createdDate',
    orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 100000,
    userId: userId.value,
  }),
)

onLoad((option: any) => {
  console.log('option', option)
  toSelect.value = option.toSelect === 'true'
})

onShow(() => {
  run()
})

onPullDownRefresh(() => {
  run().finally(() => {
    uni.stopPullDownRefresh()
  })
})

function handleEdit(item: any) {
  console.log(item)
  addressId.value = item.addressId
  addressData.value = {
    addressDetail: item.addressDetail,
    district: item.district,
    isDefault: item.isDefault,
    phone: item.phone,
    realName: item.realName,
  }
  uni.navigateTo({
    url: '/pages/addressPage/editor?type=edit',
  })
}

function handleAdd() {
  uni.navigateTo({
    url: '/pages/addressPage/editor?type=add',
  })
  // wx.navigateTo({ url: 'plugin://address-form/index' }) // 微信小程序地图地址输入服务插件
}

function handleDelete(item: any) {
  useMsgModalStore
    .confirm({
      title: '删除地址？',
    })
    .then(() => {
      addressDelApi({
        addressId: item.addressId,
      }).then(() => {
        run()
      })
    })
}

function handleSelect(data: any) {
  if (toSelect.value) {
    // TODO 选择直接变成默认，以后要改
    addressUpdateApi({
      addressDetail: data.addressDetail,
      district: data.district,
      isDefault: true,
      phone: data.phone,
      realName: data.realName,
      userId: userId.value,
      addressId: data.addressId,
    }).then(() => {
      // selectAddressId.value = data.addressId
      uni.navigateBack()
    })
  }
}

// TODO 地址还没有跟订单
</script>

<template>
  <view class="p-4 pb-10">
    <view v-if="toSelect" class="py-3">
      请选择收件地址：
    </view>
    <view
      class="mb-3 flex flex-grow-1 items-center justify-center gap-1 rd-2 bg-white p-2 text-sm"
      @click="handleAdd"
    >
      <up-icon name="plus" size="14" />
      新增
    </view>
    <view
      v-for="item in data"
      :key="item.addressId"
      class="mb-3 rd-2 bg-white p-4 text-sm"
      @click="handleSelect(item)"
    >
      <view v-if="item.isDefault" class="text-right color-red">
        默认
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">
          省市区：
        </view>
        <view>{{ item.district }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">
          详细地址与门牌号：
        </view>
        <view>{{ item.addressDetail }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">
          姓名：
        </view>
        <view>{{ item.realName }}</view>
      </view>
      <view class="f-box">
        <view class="f-label o-color-aid">
          手机号：
        </view>
        <view>{{ item.phone }}</view>
      </view>

      <view class="flex items-center gap-2">
        <view
          class="o-bg-no f-delete mt-3 flex items-center justify-center rd-2 p-2 text-sm"
          @click.stop="handleDelete(item)"
        >
          <up-icon name="trash" size="18" />
        </view>
        <view
          class="o-bg-no mt-3 flex flex-grow-1 items-center justify-center rd-2 p-2 text-sm"
          @click.stop="handleEdit(item)"
        >
          <up-icon name="edit-pen" size="18" />
          <text class="ml-1">
            编辑
          </text>
        </view>
      </view>
    </view>
    <view class="p-3" />
    <up-empty
      v-if="data?.length === 0"
      class="pb-4"
      icon="https://wx.gs1helper.com/images/common/content.png"
      text="请添收件地址信息"
    />
  </view>
</template>

<style lang="scss" scoped>
.f-box {
  display: flex;
  justify-content: space-between;
  margin-top: 0.4rem;
}

.f-label {
  min-width: 5rem;
}

.f-delete {
  width: 2rem;
}
</style>
