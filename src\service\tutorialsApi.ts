import { http } from '@/utils/http'

/**
 * 文章分类树，显示所有的分类
 * 显示大类用：教程、通知、其他，用于开发查看articleTypeId然后调用
 */
export const articleTypeTreeApi = () => http.post('/api/articleTypeTree')

export interface ArticleListParams {
  articleTypeId: number
}

export interface ArticleListData {
  articleId: number
  articleTitle: string
  articleConent?: any
  articleUrl: string
  imageUrl: string
  sortNum: number
  articleFlag: number
}

export interface ArticleListRes {
  data: [
    {
      articleList: {
        articleConent: string
        articleFlag: number
        articleId: number
        articleTitle: string
        articleUrl: string
        imageUrl: string
        sortNum: number
      }[]
      articleTypeId: number
      itemList: {
        articleTypeId: number
        typeName: string
        sortNum: number
        itemList?: any
        articleList: ArticleListData[]
      }[]
      sortNum: number
      typeName: string
    },
  ]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}

/**
 * 文章列表,只显示可用类型的文章
 * @param params
 */
export function articleListApi(params: ArticleListParams) {
  return http.post<ArticleListRes>('/api/articleList', params)
}

// 参数接口
export interface ArticleLoadParams {
  articleId: number
}

// 响应接口
export interface ArticleLoadRes {
  data: {
    articleConent: string
    articleFlag: number
    articleId: number
    articleTitle: string
    articleUrl: string
    imageUrl: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 文章加载
 * @param {object} params qry
 * @param {number} params.articleId 文章id
 * @returns
 */
export function articleLoadApi(params: ArticleLoadParams) {
  return http.post<ArticleLoadRes>('/api/articleLoad', params)
}

// 参数接口
export interface ArticlePageParams {
  articleTitle?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: string
  pageIndex?: number
  pageSize?: number
}

// 响应接口
export interface ArticlePageRes {
  data: {
    articleFlag: number
    articleId: number
    articleTitle: string
    articleUrl: string
    imageUrl: string
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}

/**
 * 文章分页,只显示可用类型的文章
 * @param {object} params qry
 * @param {string} params.articleTitle 文章标题
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @returns
 */
export function articlePageApi(params: ArticlePageParams) {
  return http.post<ArticlePageRes>('/api/articlePage', params)
}
