<route lang="json5">
{
  layout: 'tabbar',
    style: {
    navigationBarTitleText: '商品通报管理',
    },
}
</route>

<script lang="ts" setup>
import { DesignImage } from '@/components/image'
import { Color } from '@/enums/colorEnum'

function handleToInfoReportListPage() {
  uni.navigateTo({
    url: '/pages/infoReportMg/infoReportListPage',
  })
}
</script>

<template>
  <view class="p-3">
    <view class="flex space-x-2">
      <view class="flex flex-1 flex-col space-y-2">
        <view class="o-btn-light-bg-blue-border o-shadow-blue-light center shrink-0 flex-col py-3">
          <image :src="DesignImage.btnIcon.cameras" mode="widthFix" class="f-btn-icon-lg" />
          <text class="f-btn-text">
            拍照快速通报
          </text>
        </view>
        <view class="o-btn-light-bg-blue-border o-shadow-blue-light center flex-1 space-x-2">
          <image :src="DesignImage.btnIcon.file" mode="widthFix" class="f-btn-icon-sm" />
          <text class="f-btn-text">
            批量代办通报
          </text>
        </view>
      </view>
      <view class="shrink-0 space-y-2">
        <view class="o-btn-light-bg-blue-border o-shadow-blue-light f-btn-md center flex-col">
          <image :src="DesignImage.btnIcon.checkBarcode" mode="widthFix" class="f-btn-icon-md" />
          <text class="f-btn-text">
            条码外观检测
          </text>
        </view>
        <view class="f-btn-md o-btn-light-bg-blue-border o-shadow-blue-light center flex-col">
          <image :src="DesignImage.btnIcon.checkProduct" mode="widthFix" class="f-btn-icon-md" />
          <text class="f-btn-text">
            产品风险检测
          </text>
        </view>
      </view>
    </view>
    <view class="mt-3 rounded-lg bg-white p-4">
      <view class="mb-2 text-xl font-bold">
        产品通报概况
      </view>
      <view class="flex items-center justify-between text-gray">
        <view class="shrink-0">
          条码容量占用情况：
        </view>
        <view class="flex">
          <view>已使用：21</view>
          <view>
            <text class="px-1">
              /
            </text>总量：10000
          </view>
        </view>
      </view>
      <up-line-progress class="mt-2" :percentage="30" :active-color="Color.primary" />
      <view class="mt-3 flex items-center justify-between text-gray">
        <view class="shrink-0">
          快速通报情况：
        </view>
        <view class="flex">
          <view class="text-primary">
            通报中：21
          </view>
          <view>
            <text class="px-1 text-gray">
              /
            </text><text class="text-emerald-500">
              总量：10000
            </text>
          </view>
        </view>
      </view>
      <view class="mt-3 flex items-center justify-between text-gray">
        <view class="shrink-0">
          代办通报情况：
        </view>
        <view class="flex">
          <view class="text-primary">
            通报中：21
          </view>
          <view>
            <text class="px-1 text-gray">
              /
            </text><text class="text-emerald-500">
              总量：10000
            </text>
          </view>
        </view>
      </view>
      <view
        class="o-bg-primary o-shadow-blue o-shadow-blue mt-4 rounded py-2 text-center text-white"
        @click="handleToInfoReportListPage"
      >
        通报产品管理
      </view>
    </view>
    <view class="mt-3 rounded-lg bg-white p-4">
      <view class="mb-2 text-xl font-bold">
        产品风险检测情况
      </view>
      <view class="w-full text-right">
        <text class="text-primary">
          检测中：21
        </text>
        <text class="text-gray">
          <text class="px-1">
            /
          </text><text>
            检查总数：10000
          </text>
        </text>
      </view>
      <view class="mt-3 flex items-center justify-between">
        <view>
          <text class="text-gray">
            未通报
          </text>
          <text class="text-red">
            1
          </text>
        </view>
        <view class="text-primary">
          去解决 >
        </view>
      </view>
      <view class="o-bg-primary o-shadow-blue o-shadow-blue mt-4 rounded py-2 text-center text-white">
        产品风险检测管理
      </view>
    </view>
    <view class="o-pb" />
  </view>
</template>

<style lang="scss" scoped>
.f-btn-md {
  @apply py-2;
  width: 272rpx;
}

.f-btn-icon-lg {
  width: 213rpx;
}

.f-btn-icon-md {
  $w: 146rpx;
  width: $w;
}

.f-btn-icon-sm {
  width: 120rpx;
}
</style>
