<script lang="ts" setup>
import { articleLoadApi } from '@/service/tutorialsApi'
import { addLineHeight, updateVideoDimensions } from '@/utils/tool'

const props = defineProps({
  articleIds: Array<number>,
})

const content = ref<string[]>([])
const title = ref<string[]>([])

onMounted(() => {
  props.articleIds.forEach((id) => {
    articleLoadApi({
      articleId: id,
    }).then((res) => {
      // 给视频适配宽度和高度
      const r = updateVideoDimensions(res.data.articleConent)
      // 添加默认行高
      content.value.push(addLineHeight(r))
      title.value.push(res.data.articleTitle)
    })
  })
})
</script>

<template>
  <scroll-view class="f-h overflow-scroll" :scroll-y="true">
    <view v-for="(item, index) in content" :key="index" class="mb-6">
      <view
        class="mb-4 text-center text-base font-bold"
        style="word-break: break-all; line-break: anywhere"
      >
        {{ title[index] }}
      </view>
      <up-parse class="text-left text-xs" :content="item" />
    </view>
  </scroll-view>
</template>

<style lang="scss" scoped>
.f-h {
  max-height: 50vh;
}
</style>
