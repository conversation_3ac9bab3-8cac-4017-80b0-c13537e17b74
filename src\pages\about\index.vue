<route lang="json5">
{
  style: {
    navigationBarTitleText: '关于我们',
  },
}
</route>

<script lang="ts" setup>
// #ifndef MP-TOUTIAO
const accountInfo = uni.getAccountInfoSync()
// #endif

function toStatement() {
  uni.navigateTo({
    url: '/pages/about/statement',
  })
}
</script>

<template>
  <view class="f-page flex flex-col items-center justify-between overflow-hidden px-4 pt-10">
    <view class="flex flex-col items-center text-center">
      <view class="f-app-logo mb-6 w-full" />
      <view class="mb-3 text-lg font-bold">
        商用条形码生成器
      </view>
      <view class="text-sm">
        <text>Version</text>
        <!--  #ifndef MP-TOUTIAO -->
        <text class="pr-1">
          {{ accountInfo.miniProgram.version }}
        </text>
        <!--  #endif -->
      </view>
      <view class="o-color-aid mt-12 text-sm">
        <view class="o-color-primary font-bold">
          「条码帮」
        </view>
        <view>是一家专注于商品条码电子胶片</view>
        <view>制作的专业服务商。</view>
        <view class="mt-4">
          我们致力于为企业提供
        </view>
        <view>高效便捷的一站式条码解决方案。</view>
      </view>
    </view>
    <view class="o-color-aid text-center text-xs">
      <view class="mb-1 text-primary" @click="toStatement">
        《平台免责声明》及相关条款
      </view>
      <view>条码帮知识产权服务公司 版权所有</view>
      <view>Copyright © gs1helper.com All Rights Reserved.</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-page {
  height: calc(100vh - 200rpx);
}
.f-app-logo {
  height: 140rpx;
  background-image: url('https://wx.gs1helper.com/images/app_icon.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
</style>
