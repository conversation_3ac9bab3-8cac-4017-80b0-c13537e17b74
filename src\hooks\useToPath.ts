import type { ProjectType } from '@/components/descriptionStr'
import { storeToRefs } from 'pinia'
import { BarType, ServerType } from '@/enums'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'

export function useToPath() {
  // TODO useServiceStore、orderStore这两个store要整理，是否要和一起，不同业务要不要分开等
  const serviceStore = useServiceStore()
  const { descriptionServiceType } = storeToRefs(serviceStore)
  const useOrderStore = orderStore()
  const { serverType } = storeToRefs(useOrderStore)

  const toPath = (data: ProjectType, toType: 'navigateTo' | 'redirectTo' | 'switchTo') => {
    // #ifdef MP-TOUTIAO
    const callListService = [
      ServerType.designServer,
      ServerType.labelPrint,
      ServerType.renewalService,
      ServerType.registerService,
      ServerType.modifyService,
      ServerType.importedGoods,
    ]
    if (callListService.includes(ServerType[data.descriptionServiceType])) {
      tt.makePhoneCall({
        phoneNumber: '13077483992',
        fail: () => {
          uni.showToast({
            title: '请拨打客服电话',
            icon: 'none',
          })
        },
      })
      return
    }
    // #endif
    descriptionServiceType.value = data.descriptionServiceType
    if (
      data.descriptionServiceType === BarType.EAN13
      || data.descriptionServiceType === BarType.ITF14
    ) {
      serverType.value = ServerType.makeFilm
    }
    else {
      serverType.value = ServerType[data.descriptionServiceType]
    }
    switch (toType) {
      case 'navigateTo':
        uni.navigateTo({
          url: data.url,
        })
        break
      case 'redirectTo':
        uni.redirectTo({
          url: data.url,
        })
        break
      default:
        // switchTo
        uni.switchTab({
          // 这样才有后退打底
          url: '/pages/index/index',
          success: () => {
            uni.navigateTo({
              url: data.url,
            })
          },
        })
    }
  }
  return { toPath }
}
