<script setup lang="ts">
defineProps<{
  carousesList: string[]
  windowWidth: number
  safeAreaInsets: {
    top: number
    bottom: number
  }
}>()

const current = ref(0)

function handleBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="bg-white">
    <up-swiper
      v-model:current="current"
      :height="windowWidth"
      :list="carousesList"
      indicator
      :autoplay="false"
      indicator-style="right: 20px"
    >
      <template #indicator>
        <view
          class="flex justify-center rd-6 px-3 py-1"
          style="background-color: rgba(0, 0, 0, 0.35)"
        >
          <text class="text-xs color-white">
            {{ current + 1 }}/{{ carousesList.length }}
          </text>
        </view>
      </template>
    </up-swiper>
    <view
      class="f-back fixed left-4 z-1 center rd-1"
      :style="{ top: `${safeAreaInsets?.top + 6}px` }"
      @click="handleBack"
    >
      <up-icon class="ml--0.5" name="arrow-left" size="14" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.f-back {
  $w: 7vw;
  width: $w;
  height: $w;
  background-color: rgba(255, 255, 255, 0.7);
}
</style>
