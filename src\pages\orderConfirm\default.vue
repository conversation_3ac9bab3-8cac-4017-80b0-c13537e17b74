<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单确认',
  },
}
</route>

<script lang="ts" setup>
import type {
  SumbitReportOrderParams,
} from '@/service/orderApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { douYinRequestPayment, wxRequestPayment } from '@/components/mix'
import PriceBox from '@/components/Price/PriceBox.vue'
import {
  getDouYinOrderPreDataApi,
  payReportOrderApi,
  sumbitReportOrderApi,
} from '@/service/orderApi'
import { msgModalStore } from '@/store/msgModalStore'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { catchErrorAndNavigateBack } from '@/utils'

const useMsgModalStore = msgModalStore()

const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
const useOrderStore = orderStore()
const {
  couponId,
  orderCode,
  tempType,
  price,
  priceUnit,
  total,
  tempName,
  couponPrice,
  discount,
  totalPrice,
  actuallyPrice,
  discountsPrice,
  couponType,
  isSelectedCoupon,
} = storeToRefs(useOrderStore)

const btnDisable = ref(false)
const btnStr = ref('提交订单')
const agree = ref(false)
const isModalShow = ref(false)
let url = ''

onLoad((option: any) => {
  console.log(option)
  if (option.paymentSuccessUrl) {
    url = decodeURIComponent(option.paymentSuccessUrl)
  }
  useOrderStore.clearOrderInfo()
  useOrderStore.clearCouponInfo()
  getOrderPrice()
})

onShow(() => {
  // onLoad 比 onShow 优先执行
  // 选择优惠券后，后退回到这逻辑
  if (isSelectedCoupon.value) {
    getOrderPrice()
    isSelectedCoupon.value = false
  }
})

/**
 * 找出面值最大的，最低使用额度满足的代金券
 * @param list
 */
function findMaxPriceCoupon(list: any[]) {
  // 过滤出符合条件的对象，无需判断优惠券类型serverType，因为请求的是serverType===2的数据
  const filteredList = list.filter(
    item => item.couponType === 1 && item.minUsagePrice <= actuallyPrice.value,
  )

  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    return null
  }

  // 在过滤后的列表中找出couponPrice最大的对象
  return filteredList.reduce(
    (max, current) => (current.couponPrice > max.couponPrice ? current : max),
    filteredList[0],
  )
}

/**
 * 找出折扣券中折扣最小的
 * @param list
 */
function findBestDiscountCoupon(list: any[]) {
  // 过滤出符合条件的对象
  const filteredList = list.filter(item => item.couponType === 2 && item.discount)

  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    return null
  }

  return filteredList.reduce(
    (max, current) => (current.discount < max.discount ? current : max),
    filteredList[0],
  )
}

/**
 * 找出最优优惠券，
 * @param list
 */
function getOptimalCoupon(list: any) {
  // console.log(list)

  // 最优代金券列表
  const bestPriceItem = findMaxPriceCoupon(list)
  // console.log('bestPriceItem', bestPriceItem)
  // 折扣券列表
  const bestDiscountItem = findBestDiscountCoupon(list)
  // console.log('bestDiscountItem', bestDiscountItem)

  // 代金券计算出来的实付
  let pricePrice = actuallyPrice.value
  // 折扣券计算出来的实付
  let discountPrice = actuallyPrice.value
  if (bestPriceItem) {
    pricePrice = actuallyPrice.value - bestPriceItem.couponPrice
  }
  if (bestDiscountItem) {
    discountPrice = actuallyPrice.value * bestDiscountItem.discount
  }
  if (bestPriceItem === null && bestDiscountItem === null) {
    couponId.value = null
  }
  if (pricePrice <= discountPrice) {
    // 代金券优惠
    // console.log('bestPriceItem', bestPriceItem)
    couponId.value = bestPriceItem?.couponId
    couponType.value = bestPriceItem?.couponType
    discount.value = bestPriceItem?.discount
    couponPrice.value = bestPriceItem?.couponPrice
  }
  else {
    // 折扣券优惠
    // console.log('bestDiscountItem', bestDiscountItem)
    couponId.value = bestDiscountItem?.couponId
    couponType.value = bestDiscountItem?.couponType
    discount.value = bestDiscountItem?.discount
    couponPrice.value = bestDiscountItem?.couponPrice
  }
}

/**
 * 选优惠券计算优惠
 */
function getOrderPrice() {
  return new Promise((resolve, reject) => {
    btnDisable.value = true
    const params: SumbitReportOrderParams = {
      orderCode: orderCode.value,
    }
    sumbitReportOrderApi(params)
      .then((res: any) => {
        const d = res.data
        actuallyPrice.value = d.actuallyPrice
        price.value = d.price
        priceUnit.value = d.priceUnit
        total.value = d.total
        totalPrice.value = d.totalPrice
        discountsPrice.value = d.discountsPrice
        tempName.value = d.tempName
        // d.tempType 后期不用了，改用serverType
        tempType.value = d.serverType
        btnDisable.value = false
        resolve(true)
      })
      .catch((err) => {
        btnDisable.value = false
        useOrderStore.clearCouponInfo()
        reject(err)
      })
  })
}

const handleSubmit = debounce(
  () => {
    if (!btnDisable.value) {
      if (agree.value) {
        btnDisable.value = true
        uni.showLoading({
          title: '支付中',
        })
        getOrderPrice()
          .then(() => {
            // #ifdef MP-WEIXIN
            // 微信支付逻辑
            payReportOrderApi({
              fromTo: import.meta.env.VITE_FROM_PLATFORM,
              orderCode: orderCode.value,
            })
              .then((resData: any) => {
                if (!url) {
                  url = '/pages/paymentSuccess/default'
                }
                if (resData.data.isNeedToPay) {
                  wxRequestPayment(resData.data)
                    .then(() => {
                      btnDisable.value = false
                      uni.hideLoading()
                      uni.redirectTo({
                        url,
                      })
                    })
                    .catch(() => {
                      btnDisable.value = false
                      uni.hideLoading()
                    })
                }
                else {
                  btnDisable.value = false
                  uni.hideLoading()
                  uni.redirectTo({
                    url,
                  })
                }
              })
              .catch((err: string) => {
                btnDisable.value = false
                uni.hideLoading()
                catchErrorAndNavigateBack(err)
              })
            // #endif

            // #ifdef MP-TOUTIAO
            // 抖音支付逻辑
            getDouYinOrderPreDataApi({
              orderCode: orderCode.value,
              params: '',
              path: 'pages/index/index',
            }).then((res) => {
              // console.log(res.data)
              if (res.data.isZeroOrder) {
                // 无需支付
                btnDisable.value = false
                uni.hideLoading()
                uni.redirectTo({
                  url,
                })
              }
              else {
                const d = {
                  orderCode: orderCode.value,
                  ...res.data,
                }
                douYinRequestPayment(d)
                  .then(() => {
                    btnDisable.value = false
                    uni.hideLoading()
                    uni.redirectTo({
                      url,
                    })
                  })
                  .catch(() => {
                    btnDisable.value = false
                    uni.hideLoading()
                  })
              }
            })
            // #endif
          })
          .catch(() => {
            btnDisable.value = false
            uni.hideLoading()
          })
      }
      else {
        useMsgModalStore
          .confirm({
            title: '温馨提示',
            content: '请先勾选已阅读并同意《付款免责声明》',
          })
          .then(() => {
            uni.pageScrollTo({
              selector: '#agreeElement',
            })
          })
      }
    }
  },
  1000,
  { immediate: true },
)

function handleModalOk() {
  agree.value = true
  isModalShow.value = false
}
</script>

<template>
  <up-modal
    :show="isModalShow"
    confirm-text="同意"

    close-on-click-overlay show-cancel-button
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer :article-ids="DESCRIPTION_STR[descriptionServiceType].policy" />
  </up-modal>
  <view class="flex items-end justify-between bg-white p-4">
    <view class="pl-2">
      <view class="o-color-primary text-xl font-bold">
        订单确认-{{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="o-color-aid text-sm">
        {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
      </view>
      <view class="text-xs">
        {{ DESCRIPTION_STR[descriptionServiceType].description }}
      </view>
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 mt-2 font-bold">
        所选服务方案：
      </view>
      <view class="flex justify-between gap-4">
        <view>{{ tempName }}</view>
        <view class="shrink-0">
          <text class="font-bold">
            {{ price }}
          </text>
          {{ priceUnit }}
        </view>
      </view>
      <view class="o-line mb-4 mt-4" />
      <view class="flex justify-end">
        <view class="flex items-baseline">
          <view>合计：</view>
          <price-box :price="actuallyPrice" :size="48" class="o-color-danger" />
        </view>
      </view>
    </view>
    <view id="agreeElement" class="mt-6 flex items-center justify-center text-xs">
      <up-checkbox
        v-model:checked="agree"
        used-alone
        label-size="12"
        size="14"
        label="我已阅读并同意"
      />
      <view class="o-color-primary" @click.stop="isModalShow = true">
        《付款免责声明》
      </view>
    </view>
    <view class="p-11" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        :class="btnDisable ? 'o-bg-primary-disable' : ' o-bg-primary o-shadow-blue'"
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 color-white font-bold"
        @click="handleSubmit"
      >
        {{ btnStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
