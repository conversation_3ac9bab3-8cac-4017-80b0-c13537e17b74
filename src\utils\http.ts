import type { DefaultDataRes } from '@/interceptors/myInterceptors'
import type { CustomRequestOptions } from '@/interceptors/request'

export function http<T extends DefaultDataRes = DefaultDataRes>(options: CustomRequestOptions) {
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const r = res.data as T
          if (r.success) {
            // 2.1 提取核心数据 res.data
            resolve(r)
          }
          else {
            if (r.errMessage === 'Token过期') {
              uni.switchTab({
                url: '/pages/userPage/index',
              })
            }
            else {
              if (!options.hideErrorToast) {
                uni.showToast({
                  icon: 'none',
                  title: r.errMessage || '请求错误',
                })
              }
            }
            reject(r.errMessage)
          }
        }
        else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        }
        else {
          // 其他错误 -> 根据后端错误信息轻提示
          if (!options.hideErrorToast) {
            uni.showToast({
              icon: 'none',
              title: '请求错误',
            })
          }
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @returns
 */
export function httpGet<T extends DefaultDataRes>(url: string, query?: Record<string, any>) {
  return http<T>({
    url,
    query,
    method: 'GET',
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param hideErrorToast 是否隐藏报错提示
 * @returns
 */
export function httpPost<T extends DefaultDataRes>(url: string, data?: Record<string, any>, query?: Record<string, any>, hideErrorToast?: boolean, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    data,
    query,
    method: 'POST',
    hideErrorToast,
    ...options,
  })
}
/**
 * PUT 请求
 */
export function httpPut<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
    ...options,
  })
}

/**
 * DELETE 请求（无请求体，仅 query）
 */
export function httpDelete<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
    ...options,
  })
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete
