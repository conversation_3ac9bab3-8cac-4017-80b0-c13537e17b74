import type { ServerType } from '@/enums'
import { http } from '@/utils/http'

// 参数接口
export interface LoginParams {
  code: string
  dataCode?: string
  fromTo?: number
}

// 响应接口
export interface LoginRes {
  data: {
    authorization: string
    inBlacklist: boolean
    userCode: string
    userId: string
    realName: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

// 微信登录
export const wxParentsUserLoginPath = '/wxparents/user/login'
// 抖音登录
export const wxparentsUserDouYinLogin = '/wxparents/user/douYinLogin'

// 参数接口
export interface GetPhoneParams {
  code: string
  dataCode?: string
  fromTo?: number
}

// 响应接口
export interface GetPhoneRes {
  data: string
  errCode: string
  errMessage: string
  success: boolean
}

export function wxparentsUserGetPhoneApi(params: GetPhoneParams) {
  return http.post<GetPhoneRes>('/wxparents/user/getPhone', params)
}

// 参数接口
export interface SignUrlLinkParams {
  query: string
  userInfoId: number
}

// 响应接口
export interface SignUrlLinkRes {
  data: string
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 小程序来源统计
 * @param {object} params cmd
 * @param {string} params.query 生成参数
 * @returns
 */
export function wxmalinkSignUrlLinkApi(params: SignUrlLinkParams) {
  return http.post<SignUrlLinkRes>('/wxmalink/signUrlLink', params, {}, true)
}

// 参数接口
export interface GetUserServerParams {
  certificationId?: number
  userId: number
}

// 响应接口
export interface GetUserServerRes {
  data: {
    barCodeCardNum: string
    barCodeCardPassword: string
    changeAuth: boolean
    changeOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    dataCode: string
    existUncompletedChange: boolean
    existUncompletedRegister: boolean
    existUncompletedRenewal: boolean
    phone: string
    qrAuth: boolean
    qrOrderInfo: {
      capacity: number
      certificationId: number
      createdDate: string
      expiryDate: string
      flow: number
      orderCode: string
      startDate: string
      tempName: string
      tempType: number
    }
    registerAuth: boolean
    registerOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    renewalAuth: boolean
    renewalInformDTO: {
      companyName: string
      gsDataId: number
      isNeedInform: boolean
      validDate: string
      vendorCode: string
    }
    renewalOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    reportAuth: boolean
    reportOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
export function getUserServerInfoApi(params: GetUserServerParams) {
  return http.post<GetUserServerRes>('/user/getUserServerInfo', params)
}
