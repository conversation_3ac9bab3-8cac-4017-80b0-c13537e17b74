<script setup lang="ts">
import { DesignImage } from '@/components/image'

const props = defineProps<{
  data: any
}>()

function toContextPage(id: number) {
  uni.navigateTo({
    url: `/pages/tutorials/contextPage?id=${id}`,
  })
}
</script>

<template>
  <view class="mb-3 flex gap-1 rd-2 bg-white p-2 text-sm" @click="toContextPage(data.articleId)">
    <up-image
      class="shrink-0 overflow-hidden rd-1"
      :width="85"
      :height="59"
      :src="data.imageUrl ? data.imageUrl : DesignImage.tutorials.defaultMainImg"
    >
      <template #error>
        <up-image
          class="shrink-0 overflow-hidden rd-1"
          :width="85"
          :height="59"
          :src="DesignImage.tutorials.defaultMainImg"
        />
      </template>
    </up-image>
    <view class="p-2">
      <text v-if="data.articleFlag === 1" class="o-color-danger">
        【置顶】
      </text>
      <text>{{ data.articleTitle }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
