/* 使用： <scroll-view
scroll-y="true"
class="f-scroll-box o-bg-no relative"
:scroll-top="scrollTop"
@scroll="getScroll"
:refresher-enabled="true"
:refresher-triggered="refreshTriggered"
@refresherrefresh="onRefresh"
:scroll-with-animation="true"
  > */

export function useScrollViewBackTop() {
  const scrollTop = ref<number>(0)
  const oldScrollTop = ref(0)
  const flag = ref(false)
  const getScroll = (event: any) => {
    oldScrollTop.value = event.detail.scrollTop
    flag.value = oldScrollTop.value > 300
  }
  const getToTop = () => {
    scrollTop.value = oldScrollTop.value
    nextTick(() => {
      scrollTop.value = 0
    })
  }
  return {
    scrollTop,
    flag,
    getScroll,
    getToTop,
  }
}
